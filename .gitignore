# Dependencies
node_modules/
*/node_modules/
**/node_modules/
.pnp
.pnp.js

# Package managers
.pnpm-store/
.pnpm-debug.log*
**/.pnpm/
pnpm-lock.yaml
.yarn-integrity

# Production builds
build/
dist/
*/build/
*/dist/
**/build/
**/dist/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Cache directories
.npm
.cache
.parcel-cache
.eslintcache
.stylelintcache

# Build tool caches
.turbo
.vitest
jest-cache/

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Framework-specific build outputs
.next
.nuxt
.vuepress/dist
.docusaurus
.cache-loader

# Gatsby files
public

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# Database files
.dynamodb/
neo4j/
*.db
*.sqlite
*.sqlite3

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
pnpm-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# IDE and Editor files
.vscode/
.idea/
.obsidian/
*.swp
*.swo
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.tmp
*.temp

# Docker
.docker/
docker-compose.override.yml
.dockerignore.bak
