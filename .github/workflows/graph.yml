name: Graph Build

on:
  push:
    branches: [main]
  pull_request:

jobs:
  build-graph:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v2
        with: 
          version: 8.15.4
      - uses: actions/setup-node@v4
        with: 
          node-version: 20.11.0
          cache: pnpm
      - run: corepack enable
      - run: cd code && pnpm install --frozen-lockfile
      - run: cd code && pnpm build
      - run: cd code && pnpm run build-kg -- --dry-run ../docs/tech-specs
