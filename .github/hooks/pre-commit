#!/bin/sh
#
# Git pre-commit hook to run lint check on entire repository
# Ensures code quality standards before commits
#

echo "🔍 Running lint check on entire repository..."

# Change to the code directory where package.json is located
cd code

# Run lint check on entire codebase
if ! pnpm lint; then
    echo ""
    echo "❌ Lint check failed!"
    echo "🔧 Please fix the linting errors above before committing."
    echo "💡 You can run 'pnpm lint --fix' to automatically fix some issues."
    echo "🚫 To bypass this check (not recommended), use: git commit --no-verify"
    echo ""
    exit 1
fi

echo "✅ Lint check passed! Code quality looks good."
exit 0
