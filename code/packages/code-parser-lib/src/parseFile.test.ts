import { parseFile, parseCodeDirectory } from './parseFile.js';
import { writeFileSync, mkdirSync, rmSync } from 'fs';
import { join } from 'path';

describe('parseFile', () => {
  const testDir = join(__dirname, '../test-temp');

  beforeEach(() => {
    // Create test directory
    mkdirSync(testDir, { recursive: true });
  });

  afterEach(() => {
    // Clean up test directory
    rmSync(testDir, { recursive: true, force: true });
  });

  describe('Python parsing', () => {
    it('should parse Python functions correctly', () => {
      const pythonCode = `
def add(a, b):
    """Add two numbers."""
    return a + b

def multiply(x, y):
    return x * y

def main():
    result = add(1, 2)
    product = multiply(3, 4)
    return result
`;
      const filePath = join(testDir, 'test.py');
      writeFileSync(filePath, pythonCode);

      const result = parseFile(filePath);

      expect(result.errors).toHaveLength(0);
      expect(result.functions).toHaveLength(3);

      const addFunction = result.functions.find((f) => f.name === 'add');
      expect(addFunction).toBeDefined();
      expect(addFunction?.lang).toBe('python');
      expect(addFunction?.signature).toBe('add(a, b)');
      expect(addFunction?.line_start).toBe(2);

      const mainFunction = result.functions.find((f) => f.name === 'main');
      expect(mainFunction).toBeDefined();
      expect(mainFunction?.line_start).toBe(9);
    });

    it('should parse Python function calls', () => {
      const pythonCode = `
def helper():
    return 42

def main():
    result = helper()
    print(result)
    return result
`;
      const filePath = join(testDir, 'test.py');
      writeFileSync(filePath, pythonCode);

      const result = parseFile(filePath);

      expect(result.errors).toHaveLength(0);
      expect(result.calls.length).toBeGreaterThan(0);

      const helperCall = result.calls.find((c) => c.callee === 'helper');
      expect(helperCall).toBeDefined();
      expect(helperCall?.line).toBe(6);
    });
  });

  describe('JavaScript parsing', () => {
    it('should parse JavaScript functions correctly', () => {
      const jsCode = `
function add(a, b) {
    return a + b;
}

const multiply = (x, y) => {
    return x * y;
};

class Calculator {
    calculate(a, b) {
        return add(a, b);
    }
}

function main() {
    const result = add(1, 2);
    const product = multiply(3, 4);
    return result;
}
`;
      const filePath = join(testDir, 'test.js');
      writeFileSync(filePath, jsCode);

      const result = parseFile(filePath);

      expect(result.errors).toHaveLength(0);
      expect(result.functions.length).toBeGreaterThanOrEqual(4);

      const addFunction = result.functions.find((f) => f.name === 'add');
      expect(addFunction).toBeDefined();
      expect(addFunction?.lang).toBe('javascript');
      expect(addFunction?.signature).toBe('add(a, b)');

      const arrowFunction = result.functions.find(
        (f) => f.name === 'anonymous'
      );
      expect(arrowFunction).toBeDefined();

      const methodFunction = result.functions.find(
        (f) => f.name === 'calculate'
      );
      expect(methodFunction).toBeDefined();
    });

    it('should parse JavaScript function calls', () => {
      const jsCode = `
function helper() {
    return 42;
}

function main() {
    const result = helper();
    console.log(result);
    return result;
}
`;
      const filePath = join(testDir, 'test.js');
      writeFileSync(filePath, jsCode);

      const result = parseFile(filePath);

      expect(result.errors).toHaveLength(0);
      expect(result.calls.length).toBeGreaterThan(0);

      const helperCall = result.calls.find((c) => c.callee === 'helper');
      expect(helperCall).toBeDefined();
    });
  });

  describe('Error handling', () => {
    it('should handle unsupported file types', () => {
      const filePath = join(testDir, 'test.txt');
      writeFileSync(filePath, 'some text content');

      const result = parseFile(filePath);

      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]?.error).toBe('Unsupported file type');
      expect(result.functions).toHaveLength(0);
      expect(result.calls).toHaveLength(0);
    });

    it('should handle non-existent files', () => {
      const filePath = join(testDir, 'nonexistent.py');

      const result = parseFile(filePath);

      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]?.error).toBe('Parse error');
      expect(result.functions).toHaveLength(0);
      expect(result.calls).toHaveLength(0);
    });
  });

  describe('parseCodeDirectory', () => {
    it('should parse multiple files in a directory', () => {
      const pythonCode = `
def python_func():
    return "python"
`;
      const jsCode = `
function jsFunc() {
    return "javascript";
}
`;

      writeFileSync(join(testDir, 'test.py'), pythonCode);
      writeFileSync(join(testDir, 'test.js'), jsCode);

      const result = parseCodeDirectory(testDir);

      expect(result.errors).toHaveLength(0);
      expect(result.functions.length).toBeGreaterThanOrEqual(2);

      const pythonFunc = result.functions.find((f) => f.name === 'python_func');
      const jsFunc = result.functions.find((f) => f.name === 'jsFunc');

      expect(pythonFunc).toBeDefined();
      expect(pythonFunc?.lang).toBe('python');
      expect(jsFunc).toBeDefined();
      expect(jsFunc?.lang).toBe('javascript');
    });

    it('should filter by language', () => {
      const pythonCode = `def python_func(): pass`;
      const jsCode = `function jsFunc() {}`;

      writeFileSync(join(testDir, 'test.py'), pythonCode);
      writeFileSync(join(testDir, 'test.js'), jsCode);

      const result = parseCodeDirectory(testDir, ['py']);

      expect(result.functions.length).toBe(1);
      expect(result.functions[0]?.lang).toBe('python');
    });

    it('should handle directory with no code files', () => {
      writeFileSync(join(testDir, 'readme.txt'), 'No code here');

      const result = parseCodeDirectory(testDir);

      expect(result.functions).toHaveLength(0);
      expect(result.calls).toHaveLength(0);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('Function ID generation', () => {
    it('should generate deterministic IDs', () => {
      const pythonCode = `
def test_func():
    return True
`;
      const filePath = join(testDir, 'test.py');
      writeFileSync(filePath, pythonCode);

      const result1 = parseFile(filePath);
      const result2 = parseFile(filePath);

      expect(result1.functions[0]?.id).toBe(result2.functions[0]?.id);
      expect(result1.functions[0]?.id).toMatch(/^function:[a-f0-9]{8}$/);
    });
  });
});
