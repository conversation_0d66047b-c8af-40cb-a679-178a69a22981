{"name": "@workflow-mapper/code-parser-lib", "version": "0.0.1", "description": "Static code parser for knowledge graph generation using Tree-sitter", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format esm --dts", "type-check": "tsc --noEmit", "test": "jest", "test:coverage": "jest --coverage"}, "keywords": ["tree-sitter", "parser", "knowledge-graph", "static-analysis", "python", "javascript"], "author": "WorkflowMapper Team", "license": "ISC", "dependencies": {"tree-sitter": "0.22.3", "tree-sitter-javascript": "0.23.1", "tree-sitter-python": "0.23.6", "tree-sitter-typescript": "^0.23.2", "uuid": "9.0.0"}, "devDependencies": {"@types/jest": "29.5.12", "@types/node": "20.12.7", "@types/uuid": "10.0.0", "esbuild-register": "3.4.2", "jest": "29.7.0", "tree-sitter-cli": "0.25.4", "ts-jest": "29.1.2", "tsup": "8.0.2", "typescript": "5.4.3"}}