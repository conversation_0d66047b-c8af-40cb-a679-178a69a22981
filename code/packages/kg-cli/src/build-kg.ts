#!/usr/bin/env node

import { parseArgs } from 'node:util';
import { buildKnowledgeGraph } from './index.js';

interface CliArgs {
  'dry-run'?: boolean;
  help?: boolean;
}

function showHelp() {
  console.log(`
Usage: build-kg [options] <directory>

Build a knowledge graph from MDX specification files.

Arguments:
  directory         Directory containing MDX files to parse

Options:
  --dry-run        Print summary without writing files
  --help           Show this help message

Examples:
  build-kg docs/tech-specs
  build-kg --dry-run docs/tech-specs
`);
}

async function main() {
  try {
    // Filter out npm/pnpm artifacts like "--"
    const args = process.argv.slice(2).filter(arg => arg !== '--');

    const { values, positionals } = parseArgs({
      args,
      options: {
        'dry-run': {
          type: 'boolean',
          default: false,
        },
        help: {
          type: 'boolean',
          default: false,
        },
      },
      allowPositionals: true,
    }) as { values: CliArgs; positionals: string[]; };

    if (values.help) {
      showHelp();
      process.exit(0);
    }

    const directory = positionals[0];
    if (!directory) {
      console.error('Error: Directory argument is required');
      showHelp();
      process.exit(1);
    }

    const isDryRun = values['dry-run'] || false;

    console.log(`🔗 Building knowledge graph from: ${directory}`);
    if (isDryRun) {
      console.log('📋 Dry run mode - no files will be written');
    }

    const result = await buildKnowledgeGraph(directory, { dryRun: isDryRun, outputDir: '.' });

    if (isDryRun) {
      console.log('\n📊 Summary:');
      console.log(`  Specs found: ${result.summary.specsCount}`);
      console.log(`  Milestones: ${result.summary.milestonesCount}`);
      console.log(`  Components: ${result.summary.componentsCount}`);
      console.log(`  Relationships: ${result.summary.relationshipsCount}`);

      if (result.errors.length > 0) {
        console.log(`\n⚠️  Errors: ${result.errors.length}`);
        result.errors.forEach(error => {
          console.log(`  - ${error.filePath}: ${error.error}`);
        });
      }
    } else {
      console.log(`\n✅ Knowledge graph built successfully!`);
      console.log(`  📄 JSON-LD: ${result.files.jsonld}`);
      console.log(`  📄 YAML: ${result.files.yaml}`);
      console.log(`  📊 Specs processed: ${result.summary.specsCount}`);

      if (result.errors.length > 0) {
        console.log(`\n⚠️  Warnings: ${result.errors.length}`);
        result.errors.forEach(error => {
          console.log(`  - ${error.filePath}: ${error.error}`);
        });
      }
    }

    // Exit with error code if there were parsing errors
    if (result.errors.length > 0) {
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ Error:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Only run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
