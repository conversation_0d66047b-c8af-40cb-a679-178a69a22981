#!/bin/sh

# Script to set up Git hooks
echo "Setting up Git hooks..."

# Ensure Git uses standard hooks directory (not <PERSON>sky)
cd ..
git config --unset core.hookspath 2>/dev/null || true
cd code

# Create hooks directory if it doesn't exist
mkdir -p ../.git/hooks

# Create hooks directory for storing hooks
mkdir -p ../.github/hooks

# Create commit-msg hook if it doesn't exist in .github/hooks
if [ ! -f ../.github/hooks/commit-msg ]; then
    cat > ../.github/hooks/commit-msg << 'EOF'
#!/bin/sh
#
# Git commit-msg hook to enforce semantic versioning commit messages
# Platform and language agnostic solution
#

commit_file="$1"
commit_message=$(cat "$commit_file")

# Remove any leading/trailing whitespace
commit_message=$(echo "$commit_message" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

# Check if commit message is empty
if [ -z "$commit_message" ]; then
    echo "❌ Commit message cannot be empty!"
    exit 1
fi

# Define valid commit types
valid_types="feat|fix|docs|style|refactor|perf|test|build|ci|chore|revert"

# Check if commit message follows semantic format
if ! echo "$commit_message" | grep -Eq "^($valid_types)(\([a-zA-Z0-9._-]+\))?: .+$"; then
    echo "❌ Invalid commit message format!"
    echo "📝 Your message: '$commit_message'"
    echo ""
    echo "✅ Commit messages must follow this format:"
    echo "   type(scope): description"
    echo ""
    echo "Valid types:"
    echo "   • feat     : A new feature"
    echo "   • fix      : A bug fix"
    echo "   • docs     : Documentation changes"
    echo "   • style    : Code style changes (formatting, etc)"
    echo "   • refactor : Code refactoring"
    echo "   • perf     : Performance improvements"
    echo "   • test     : Adding or fixing tests"
    echo "   • build    : Build system changes"
    echo "   • ci       : CI configuration changes"
    echo "   • chore    : Other changes"
    echo "   • revert   : Revert a previous commit"
    echo ""
    echo "Scope is optional. Description is required."
    echo "Scope can contain: letters, numbers, periods, underscores, hyphens"
    echo ""
    echo "Examples of good commit messages:"
    echo "   • feat(auth): add user authentication"
    echo "   • fix(M1.2): resolve parsing issue"
    echo "   • docs(user_guide): update installation guide"
    echo "   • refactor(api-service): simplify user service"
    echo "   • feat(kg-sync_lib): add annotation parser"
    echo ""
    exit 1
fi

# Check if commit message is too long (max 500 characters)
message_length=$(echo "$commit_message" | wc -c)
if [ "$message_length" -gt 500 ]; then
    echo "❌ Commit message is too long!"
    echo "📏 Current length: $message_length characters (max: 500)"
    echo "📝 Your message: '$commit_message'"
    echo ""
    echo "✅ Commit messages should be:"
    echo "   • Maximum 500 characters"
    echo "   • Clear and concise"
    echo "   • Follow the format: type(scope): description"
    echo ""
    exit 1
fi

echo "✅ Commit message follows semantic convention and looks good! ($message_length chars)"
exit 0
EOF
    chmod +x ../.github/hooks/commit-msg
fi

# Create pre-commit hook for lint checking if it doesn't exist
if [ ! -f ../.github/hooks/pre-commit ]; then
    cat > ../.github/hooks/pre-commit << 'EOF'
#!/bin/sh
#
# Git pre-commit hook to run lint check on entire repository
# Ensures code quality standards before commits
#

echo "🔍 Running lint check on entire repository..."

# Change to the code directory where package.json is located
cd code

# Run lint check on entire codebase
if ! pnpm lint; then
    echo ""
    echo "❌ Lint check failed!"
    echo "🔧 Please fix the linting errors above before committing."
    echo "💡 You can run 'pnpm lint --fix' to automatically fix some issues."
    echo "🚫 To bypass this check (not recommended), use: git commit --no-verify"
    echo ""
    exit 1
fi

echo "✅ Lint check passed! Code quality looks good."
exit 0
EOF
    chmod +x ../.github/hooks/pre-commit
fi

# Copy hooks to git hooks directory
cp ../.github/hooks/commit-msg ../.git/hooks/
cp ../.github/hooks/pre-commit ../.git/hooks/

# Make hooks executable
chmod +x ../.git/hooks/commit-msg
chmod +x ../.git/hooks/pre-commit

echo "✅ Git hooks setup complete!"
echo "📋 Configured hooks:"
echo "   • pre-commit: Runs lint check on entire repository"
echo "   • commit-msg: Validates semantic versioning format"
echo ""
echo "💡 To bypass hooks (not recommended): git commit --no-verify"
