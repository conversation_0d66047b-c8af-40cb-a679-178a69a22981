#!/bin/sh

# Script to set up Git hooks
echo "Setting up Git hooks..."

# Create hooks directory if it doesn't exist
mkdir -p ../.git/hooks

# Create hooks directory for storing hooks
mkdir -p .github/hooks

# Copy commit-msg hook if it doesn't exist in .github/hooks
if [ ! -f .github/hooks/commit-msg ]; then
    cp ../.github/hooks/commit-msg .github/hooks/
fi

# Copy commit-msg hook to git hooks directory
cp .github/hooks/commit-msg ../.git/hooks/
chmod +x ../.git/hooks/commit-msg

echo "✅ Git hooks setup complete!"
echo "Commit messages will now be validated according to semantic versioning format."
