# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Initial project setup
- Monorepo structure with pnpm workspaces
- Express API server with health endpoint
- React web application with Vite
- Shared utilities package
- TypeScript configuration
- ESLint and Prettier setup
- Jest and Vitest testing setup
- Docker configuration
- GitHub Actions CI pipeline
- Pre-commit hooks with <PERSON>sky
- Comprehensive documentation

### Changed
- N/A

### Deprecated
- N/A

### Removed
- N/A

### Fixed
- N/A

### Security
- N/A

## [0.0.1] - 2025-05-25

### Added
- Initial repository setup
- Basic project structure
- Development toolchain configuration

---

## Release Notes

### v0.0.1 - Initial Release

This is the initial release of Workflow Mapper, establishing the foundational infrastructure for the project.

**Key Features:**
- ✅ Monorepo setup with pnpm workspaces
- ✅ TypeScript-first development environment
- ✅ Express API with health endpoints
- ✅ React web application with Vite
- ✅ Comprehensive testing setup (Jest + Vitest)
- ✅ Docker containerization
- ✅ CI/CD pipeline with GitHub Actions
- ✅ Code quality tools (ESLint, Prettier, Husky)

**Technical Specifications:**
- Node.js 20.11.0+
- pnpm 8.15.4
- TypeScript 5.4.3
- Express 4.19.2
- React 18.2.0
- Vite 5.2.2

**Documentation:**
- Complete setup and development guides
- Contributing guidelines
- Security policy
- Technical specifications in `docs/tech-specs/`

This release implements [Milestone M0](./docs/tech-specs/milestones/milestone-M0.mdx) as specified in the technical documentation.
