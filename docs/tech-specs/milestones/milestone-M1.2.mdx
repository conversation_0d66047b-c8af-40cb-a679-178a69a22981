---
title: Milestone M1.1 — Bidirectional Sync & Incremental Diff
description: Link code ↔ specs via annotations, update the KG on every git diff, and emit confidence / coverage metrics.
created: 2025-05-29
version: 0.1.0
status: Draft
tags: [milestone]
authors: [WorkflowMapper Team]
---

import { Callout } from '@/components/Callout'

<Callout emoji="↔️">
<strong>Objective:</strong> Close the loop—when specs change, generate TODO stubs in code; when code changes, refresh <code>kg.*</code> and flag stale specs.<br/>
All updates run incrementally via <code>git diff</code>; the KG gains <code>confidence</code> and <code>last_verified</code> metadata.
</Callout>

---

## 🧳 Toolchain Versions

```yaml
node: "20.11.0"
pnpm: "8.15.4"
typescript: "5.4.3"
simple-git: "3.22.0"          # git diff wrapper
comment-parser: "1.4.0"       # parse JS/TS docblocks
regexparam: "2.0.0"           # lightweight pattern match
jest: "29.7.0"
# plus dependencies from M0.2 and M1—keep same pin versions
```

---

## 🎯 Definition of Done

1. **Annotations**: JS/TS functions annotated with
   ```js
   /** @implements milestone-MX #ComponentName */
   ```
   are parsed into implements edges (confidence = 1.0).
2. **Incremental CLI**: `pnpm run sync-kg -- --since <gitRef>`
   - Scans only changed files & specs.
   - Updates kg.jsonld & kg.yaml.
   - Produces a diff report (`kg-changes.json`) listing nodes/edges added, removed, or flagged stale.
3. **Confidence & coverage**: Each milestone node gains
   - `implementation_coverage` (0-1)
   - `confidence` (0-1 avg of edges).
4. **CI job** `sync-diff` runs on PR; fails if coverage of any milestone falls below 0.5.
5. **Unit-test coverage** for new sync code ≥ 80 %.

---

## 📦 Deliverables

| Artefact / Path                        | Content                                              |
|----------------------------------------|------------------------------------------------------|
| code/packages/kg-sync-lib/             | diffGit.ts, updateGraph.ts, tests                    |
| code/packages/kg-cli/                  | new command sync-kg.ts + bin entry                   |
| kg-changes.json                        | JSON diff report (only in working tree; not committed) |
| .github/workflows/sync-diff.yml        | CI flow for incremental sync                         |
| docs/tech-specs/domains/kg-sync.mdx    | Domain spec: annotation grammar, diff algorithm      |

---

## 🗂 Directory Additions

```text
code/packages/
└─ kg-sync-lib/
   ├─ src/
   │  ├─ diffGit.ts
   │  ├─ updateGraph.ts
   │  └─ index.ts
   ├─ tests/
   └─ package.json
```

---

## 🧠 Key Decisions

| Topic             | Decision                                         | Rationale                                 |
|-------------------|-------------------------------------------------|-------------------------------------------|
| Annotation format | @implements milestone-ID#Component in JSDoc/TSDoc| Simple, language-agnostic.                |
| Diff source       | Use simple-git to get changed paths vs origin/main| No external Git install assumptions in CI. |
| Stale detection   | If annotated component no longer exists, mark edge confidence = 0.2 and add stale: true. | Highlights technical debt without deleting history. |
| Coverage metric   | implementedComponents / totalComponents per milestone. | Enables red/yellow/green dashboards later. |

---

## 🔨 Task Breakdown

| #   | Branch                | Task                                         | Owner |
|-----|-----------------------|----------------------------------------------|-------|
| 01  | m1.1/sync-lib-init    | Scaffold kg-sync-lib (tsconfig, jest).       | BE    |
| 02  | m1.1/git-diff         | Implement diffGit.ts → returns changed file lists. | BE |
| 03  | m1.1/annotations      | Parse @implements tags using comment-parser. | BE    |
| 04  | m1.1/update-graph     | Implement graph merge + confidence/coverage calc. | BE |
| 05  | m1.1/cli              | Add sync-kg.ts command to kg-cli.            | BE    |
| 06  | m1.1/tests            | Jest tests: diff, annotation parse, graph update. | BE |
| 07  | m1.1/ci               | Add .github/workflows/sync-diff.yml.         | DevOps|
| 08  | m1.1/domain-doc       | Write kg-sync.mdx domain spec.               | PM    |
| 09  | m1.1/spec-quality     | Run spec-lint; set spec Approved.            | PM    |
| 10  | m1.1/final-tag        | Merge & tag kg-sync-v1.1.0.                  | Lead  |

<Callout emoji="🗂">One PR per task. Reviewers tick acceptance hint in PR description.</Callout>

---

## 🤖 CLI Specification (sync-kg)

**Usage:**

```bash
pnpm run sync-kg [--since <commit-ish>] [--dry-run]
```

- Default `--since` = merge-base with origin/main.
- Outputs:
  - Updated kg.* (unless --dry-run)
  - kg-changes.json diff summary
  - Coverage table per milestone.
- Exit statuses:
  - 0 = success, coverage OK
  - 60 = coverage drop below threshold
  - 70 = annotation parse error
  - 1 = unexpected error

---

## 🤖 CI Workflow (.github/workflows/sync-diff.yml)

```yaml
name: KG Sync (PR)
on: pull_request

jobs:
  sync-diff:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with: { fetch-depth: 0 }     # need full history for diff
      - uses: pnpm/action-setup@v2
        with: { version: 8.15.4 }
      - run: corepack enable && pnpm install
      - run: pnpm run sync-kg -- --since origin/main --dry-run
# Job fails if CLI exits non-zero (coverage breach or parse error).
```

---

## 🧪 Acceptance Tests

### 1️⃣ Annotation parse
```bash
node - <<'JS'
const { parseAnnotations } = require('code/packages/kg-sync-lib');
console.log(parseAnnotations('/** @implements milestone-M0#AuthService */').length === 1);
JS
```

### 2️⃣ Graph update
```bash
# Make a temp branch, add annotated fn, run sync-kg --since HEAD~1
pnpm run sync-kg -- --since HEAD~1
jq '.edges[] | select(.type=="implements")' kg.jsonld | wc -l  # >=1
```

### 3️⃣ Coverage threshold
```bash
# Remove an annotation, run sync-kg; CLI should set milestone coverage < 1 and still exit 0 (above 0.5). Remove a second, coverage <0.5, CLI exits 60.
```

### 4️⃣ CI green
```bash
# Push PR → sync-diff job passes.
```

---

## ✅ Success Checklist

- [ ] **SC-1** CLI parses annotations, writes updated KG.
- [ ] **SC-2** Coverage % computed & stored.
- [ ] **SC-3** CI sync-diff job fails on coverage < 0.5, passes otherwise.
- [ ] **SC-4** Unit + integration tests ≥ 80 % coverage.
- [ ] **SC-5** Spec passes spec-lint.

When all criteria are green, merge to main, tag `kg-sync-v1.1.0`, then open Milestone M2.

