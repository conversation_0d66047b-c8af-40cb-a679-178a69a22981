---
title: "Core Agent Rules"
description: "Universal executable rules for all AI software engineering agents"
created: "2025-05-25"
updated: "2025-05-25"
version: "1.0.0"
status: "Active"
tags: ["agent-rules", "core", "executable"]
authors: ["nitishMehrotra"]
---

# Core Agent Rules

> **🎯 Purpose:** Universal executable rules for all AI agents implementing milestones. For detailed process understanding, see [Core Processes](../core/).

---

## 🚀 QUICK REFERENCE

### Critical Quality Thresholds
- **Test Coverage**: Unit (80%), Integration (70%), E2E (60%)
- **Code Quality**: ESLint score >8.0, Complexity <10
- **Performance**: Response time <200ms, Bundle size <1MB
- **Security**: Zero high/critical vulnerabilities
- **Documentation**: 100% API coverage, All links working

### Emergency Response Times
- **Critical (P0)**: 15 minutes - System down, data loss, security breach
- **High (P1)**: 1 hour - Major functionality broken, significant degradation
- **Medium (P2)**: 4 hours - Minor issues, moderate performance impact
- **Low (P3)**: 24 hours - Cosmetic issues, documentation errors

### Essential Documentation Requirements
- **Real-time sync**: Max 15-minute lag between implementation and documentation
- **Template compliance**: Use appropriate templates, replace ALL placeholders
- **Validation**: Run `node docs/scripts/spec-lint.mjs` before submission
- **Required sections**: All milestone specs must include 9 specific headings

### Validation Commands
```bash
# Validate milestone specification
node docs/scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-{MILESTONE_ID}.mdx

# Validate documentation structure
node docs/scripts/validate-structure.mjs

# Run acceptance tests
bash docs/scripts/acceptance/{milestone_script}-acceptance.sh
```

---

## 🚨 MANDATORY ACTIONS

### Pre-Implementation (MUST DO FIRST)
- [ ] Create `work-log/milestone-{id}/requirement-checklist.md` BEFORE starting
- [ ] Validate ALL success criteria are understood and testable
- [ ] Plan complete dependency compatibility matrix upfront
- [ ] Design shared configuration strategy to prevent duplication
- [ ] Verify development environment is properly configured

### During Implementation (MUST DO CONTINUOUSLY)
- [ ] Update work logs in real-time (maximum 15-minute lag)
- [ ] Update milestone specification with actual versions during implementation
- [ ] Use systematic requirement tracking with checklists
- [ ] Validate each success criterion immediately after implementation
- [ ] Use package managers ONLY (never edit package files manually)
- [ ] Run tests continuously, not just at the end

### Post-Implementation (MUST DO IMMEDIATELY)
- [ ] Run `bash docs/scripts/acceptance/{milestone}-acceptance.sh` immediately after implementation
- [ ] Update all documentation to reflect actual implementation
- [ ] Create comprehensive work log with lessons learned
- [ ] Commit with conventional commit messages including detailed descriptions
- [ ] Perform security and performance validation

## ❌ PROHIBITED ACTIONS

### NEVER DO THESE
- [ ] NEVER skip success criteria (implement ALL specified criteria)
- [ ] NEVER edit package files manually (use package managers only)
- [ ] NEVER delay documentation updates (real-time sync required)
- [ ] NEVER assume requirements without validation
- [ ] NEVER proceed without passing acceptance tests

## ✅ QUALITY GATES

### Code Quality (MUST ACHIEVE)
- [ ] ALL specified success criteria implemented (no skipping)
- [ ] Minimum test coverage thresholds achieved
- [ ] All linting and type checking passed
- [ ] Comprehensive error handling included
- [ ] Security requirements validated
- [ ] Performance benchmarks met

### Validation (MUST COMPLETE)
- [ ] All acceptance criteria validated before marking complete
- [ ] Complete test suite passed before submission
- [ ] All dependencies properly managed via package managers
- [ ] All documentation synchronized with implementation
- [ ] Configuration files validated and correct
- [ ] No security vulnerabilities introduced

## 🚨 ERROR HANDLING

### File Path and Creation Rules (MUST FOLLOW)
- [ ] ALWAYS read full file paths, never use partial paths
- [ ] NEVER create files at root level without explicit permission
- [ ] NEVER create files in any directory except `code/` without explicit permission
- [ ] ASK for permission before creating files outside `code/` directory
- [ ] VERIFY file paths are complete and accurate before any file operations

### When Errors Occur (MUST DO)
- [ ] STOP implementation when critical errors detected
- [ ] Document all errors and resolution attempts
- [ ] Seek human intervention for unresolvable issues
- [ ] Maintain rollback capability at all times
- [ ] Update error handling procedures based on experience

### Success Thresholds (MUST ACHIEVE)
- [ ] 95% first-pass acceptance test success
- [ ] <15 minutes documentation lag time
- [ ] Zero post-implementation fixes required
- [ ] 100% pre-implementation validation completion
- [ ] Real-time work log updates maintained

## 📚 DETAILED PROCESS REFERENCES

For comprehensive process understanding, see these core process documents:

### Implementation Process
- **Milestone Execution**: [`../core/milestone-implementation.mdx`](../core/milestone-implementation.mdx)
- **Quality Assurance**: [`../core/quality-assurance.mdx`](../core/quality-assurance.mdx)
- **Error Recovery**: [`../core/error-recovery.mdx`](../core/error-recovery.mdx)

### Development Processes
- **Git Workflow**: [`../core/git-workflow.mdx`](../core/git-workflow.mdx)
- **Documentation Standards**: [`../core/documentation.mdx`](../core/documentation.mdx)
- **Architectural Decisions**: [`../core/architectural-decisions.mdx`](../core/architectural-decisions.mdx)

### Templates & Tools
- **Work Log Template**: [`../templates/work-log-template.mdx`](../templates/work-log-template.mdx)
- **Requirement Checklist**: [`../templates/requirement-checklist.mdx`](../templates/requirement-checklist.mdx)

## 🚨 ERROR HANDLING

### Immediate Response Protocol (First 15 Minutes)
1. **Acknowledge incident** and assign severity level
2. **Assess immediate impact** on users and systems
3. **Determine rollback necessity** for critical issues
4. **Notify stakeholders** according to severity
5. **Begin investigation** and document in tracking system

### Rollback Decision Criteria
Execute rollback when:
- **Critical system failure** cannot be quickly fixed
- **Data integrity** is at risk
- **Security vulnerability** is actively exploited
- **Fix timeline** exceeds acceptable downtime

### Recovery Commands
```bash
# Git-based rollback to previous stable commit
git checkout main && git reset --hard <previous-stable-commit>
git push --force-with-lease origin main

# Database rollback and restore
npm run migrate:rollback
pg_restore --clean --if-exists -d database_name backup_file.sql
```

---

## 🔧 CONFIGURATION ESSENTIALS

### Universal Requirements (ALL AGENTS)
- [ ] Replace ALL placeholders: {MILESTONE_ID}, {MILESTONE_TITLE}, {milestone_id}
- [ ] Run validation scripts before starting implementation
- [ ] Follow standardized work log format from templates
- [ ] Update progress systematically with checklists
- [ ] Pass all quality gates before proceeding to next phase

### Agent-Specific Extensions
These core rules are extended by agent-specific configurations:
- **Augment Agent**: [`augment.mdx`](./augment.mdx)
- **Claude/Anthropic**: [`claude.mdx`](./claude.mdx)
- **GitHub Copilot**: [`copilot.mdx`](./copilot.mdx)
- **Cursor**: [`cursor.mdx`](./cursor.mdx)
- **Custom Agents**: [`custom.mdx`](./custom.mdx)

### Validation Tools
- **Configuration Validation**: [`validation.mdx`](./validation.mdx)

## 🔄 CONTINUOUS IMPROVEMENT

### After Each Milestone (MUST DO)
- [ ] Capture lessons learned in work log
- [ ] Identify process gaps that caused issues
- [ ] Propose specific improvements for future milestones
- [ ] Update agent rules based on implementation experience
- [ ] Share learnings across all agent configurations

### Update Triggers
Update these rules when:
- Common issues identified across milestones
- New agent capabilities become available
- Quality metrics decline below thresholds
- Process inefficiencies discovered
- New security requirements emerge

---

**Rule Authority**: Development Team
**Review Frequency**: Monthly or after significant issues
**Last Updated**: 2025-05-25
