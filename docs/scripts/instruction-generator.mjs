#!/usr/bin/env node

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Milestone Instruction Generator
 *
 * Generates comprehensive, agent-specific instructions for milestone execution
 * Based on milestone specifications and agent-specific rules
 */
class InstructionGenerator {
  constructor() {
    this.supportedAgents = ["cursor", "copilot", "augment", "claude"];
  }

  /**
   * Main entry point - generates instructions for a milestone and agent
   */
  async generateInstructions(milestoneFile, agentType, outputFile) {
    try {
      console.log(`🔍 Loading milestone: ${milestoneFile}`);
      const milestone = await this.parseMilestone(milestoneFile);

      console.log(`📖 Loading agent rules: ${agentType}`);
      const agentRules = await this.loadAgentRules(agentType);

      console.log(`🔧 Loading repository processes...`);
      const repoProcesses = await this.loadRepositoryProcesses();

      console.log(`📝 Generating execution protocol...`);
      const instructions = this.generateExecutionProtocol(
        milestone,
        agentRules,
        repoProcesses,
        agentType,
        milestoneFile
      );

      console.log(`💾 Writing to: ${outputFile}`);
      await this.writeInstructions(instructions, outputFile);

      return true;
    } catch (error) {
      console.error(`❌ Error generating instructions: ${error.message}`);
      return false;
    }
  }

  /**
   * Parse milestone MDX file and extract key information
   */
  async parseMilestone(milestoneFile) {
    const content = fs.readFileSync(milestoneFile, "utf8");

    // Extract frontmatter
    const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
    const frontmatter = frontmatterMatch
      ? this.parseFrontmatter(frontmatterMatch[1])
      : {};

    // Extract main content
    const mainContent = content.replace(/^---\n[\s\S]*?\n---\n/, "");

    // Extract sections
    const sections = this.extractSections(mainContent);

    return {
      frontmatter,
      content: mainContent,
      sections,
      title: frontmatter.title || "Unknown Milestone",
      id: frontmatter.milestone || path.basename(milestoneFile, ".mdx"),
      description:
        frontmatter.description ||
        sections.overview ||
        "No description available"
    };
  }

  /**
   * Parse YAML frontmatter
   */
  parseFrontmatter(frontmatterText) {
    const frontmatter = {};
    const lines = frontmatterText.split("\n");

    for (const line of lines) {
      const match = line.match(/^(\w+):\s*(.+)$/);
      if (match) {
        frontmatter[match[1]] = match[2].replace(/^["']|["']$/g, "");
      }
    }

    return frontmatter;
  }

  /**
   * Extract sections from markdown content
   */
  extractSections(content) {
    const sections = {};
    const lines = content.split("\n");
    let currentSection = null;
    let currentContent = [];

    for (const line of lines) {
      const headerMatch = line.match(/^#{1,3}\s+(.+)$/);

      if (headerMatch) {
        // Save previous section
        if (currentSection) {
          sections[currentSection] = currentContent.join("\n").trim();
        }

        // Start new section
        currentSection = headerMatch[1]
          .toLowerCase()
          .replace(/[^\w\s]/g, "")
          .replace(/\s+/g, "_");
        currentContent = [];
      } else if (currentSection) {
        currentContent.push(line);
      }
    }

    // Save last section
    if (currentSection) {
      sections[currentSection] = currentContent.join("\n").trim();
    }

    return sections;
  }

  /**
   * Load agent-specific rules from existing documentation
   */
  async loadAgentRules(agentType) {
    const agentRulesPath = path.join(
      __dirname,
      "..",
      "tech-specs",
      "process",
      "agent-rules",
      `${agentType}.mdx`
    );
    const coreRulesPath = path.join(
      __dirname,
      "..",
      "tech-specs",
      "process",
      "agent-rules",
      "core.mdx"
    );

    let agentRules = "";
    let coreRules = "";

    try {
      if (fs.existsSync(agentRulesPath)) {
        agentRules = fs.readFileSync(agentRulesPath, "utf8");
      }

      if (fs.existsSync(coreRulesPath)) {
        coreRules = fs.readFileSync(coreRulesPath, "utf8");
      }
    } catch (error) {
      console.warn(`⚠️  Could not load agent rules: ${error.message}`);
    }

    return {
      agentSpecific: agentRules,
      core: coreRules,
      summary: this.extractRulesSummary(agentRules, coreRules)
    };
  }

  /**
   * Extract key rules summary from agent documentation
   */
  extractRulesSummary(agentRules, coreRules) {
    const rules = [];

    // Extract rules from both agent-specific and core rules
    const allRules = agentRules + "\n" + coreRules;

    // Look for numbered lists, bullet points, or "must" statements
    const rulePatterns = [
      /^\d+\.\s+(.+)$/gm, // Numbered lists
      /^[-*]\s+(.+)$/gm, // Bullet points
      /^[-*]\s+\[\s*[x\s]\s*\]\s+(.+)$/gm, // Checkbox bullet points
      /must\s+(.+?)(?:\.|$)/gi, // "Must" statements
      /should\s+(.+?)(?:\.|$)/gi, // "Should" statements
      /\*\*ALWAYS\s+(.+?)\*\*:/gi, // "**ALWAYS ...**:" statements
      /\*\*NEVER\s+(.+?)\*\*:/gi, // "**NEVER ...**:" statements
      /\*\*File\s+(.+?)\*\*:/gi, // "**File ...**:" statements
      /\*\*Ask\s+(.+?)\*\*:/gi, // "**Ask ...**:" statements
      /\*\*Verify\s+(.+?)\*\*:/gi, // "**Verify ...**:" statements
      /ALWAYS\s+(.+?)(?:\.|$)/gi, // "ALWAYS" statements
      /NEVER\s+(.+?)(?:\.|$)/gi // "NEVER" statements
    ];

    for (const pattern of rulePatterns) {
      const matches = allRules.matchAll(pattern);
      for (const match of matches) {
        if (match[1] && match[1].length > 10) {
          rules.push(match[1].trim());
        }
      }
    }

    return rules.slice(0, 10); // Top 10 most important rules
  }

  /**
   * Load repository-specific processes and conventions
   */
  async loadRepositoryProcesses() {
    const processes = {
      packageManager: "pnpm",
      gitWorkflow: [],
      testingProcedures: [],
      documentationRequirements: [],
      validationScripts: [],
      codeStyle: []
    };

    try {
      // Load process documentation if it exists
      const processPath = path.join(__dirname, "..", "tech-specs", "process");

      if (fs.existsSync(processPath)) {
        // Look for process files
        const processFiles = fs.readdirSync(processPath);

        for (const file of processFiles) {
          if (file.endsWith(".mdx") || file.endsWith(".md")) {
            const content = fs.readFileSync(
              path.join(processPath, file),
              "utf8"
            );
            this.extractProcessInfo(content, processes);
          }
        }
      }

      // Add default repository conventions
      this.addDefaultProcesses(processes);
    } catch (error) {
      console.warn(`⚠️  Could not load repository processes: ${error.message}`);
      this.addDefaultProcesses(processes);
    }

    return processes;
  }

  /**
   * Extract process information from documentation
   */
  extractProcessInfo(content, processes) {
    // Extract package manager info
    if (content.includes("pnpm")) {
      processes.packageManager = "pnpm";
    } else if (content.includes("npm")) {
      processes.packageManager = "npm";
    } else if (content.includes("yarn")) {
      processes.packageManager = "yarn";
    }

    // Extract git workflow patterns
    const gitPatterns = [
      /git\s+checkout\s+-b\s+(.+)/gi,
      /branch\s+naming[:\s]+(.+)/gi,
      /commit\s+message[:\s]+(.+)/gi
    ];

    for (const pattern of gitPatterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        if (match[1]) {
          processes.gitWorkflow.push(match[1].trim());
        }
      }
    }

    // Extract testing procedures
    if (content.includes("acceptance test") || content.includes("test")) {
      processes.testingProcedures.push(
        "Run acceptance tests after implementation"
      );
    }
    if (content.includes("pnpm test") || content.includes("npm test")) {
      processes.testingProcedures.push(
        "Execute test suite with package manager"
      );
    }
  }

  /**
   * Add default repository processes
   */
  addDefaultProcesses(processes) {
    // Default git workflow
    if (processes.gitWorkflow.length === 0) {
      processes.gitWorkflow = [
        "Create feature branches for milestone work",
        "Use descriptive commit messages",
        "Test before committing"
      ];
    }

    // Default testing
    if (processes.testingProcedures.length === 0) {
      processes.testingProcedures = [
        "Run acceptance tests after implementation",
        "Validate all success criteria",
        "Check for build errors"
      ];
    }

    // Default documentation
    processes.documentationRequirements = [
      "Update work-logs in real-time during implementation",
      "Document any issues or deviations",
      "Create execution log for milestone"
    ];

    // Default validation
    processes.validationScripts = [
      "Use validation scripts in docs/scripts/ if available",
      "Check milestone success criteria",
      "Verify all deliverables are present"
    ];

    // Default code style
    processes.codeStyle = [
      "Follow existing code patterns in the repository",
      "Use consistent naming conventions",
      "Maintain clean, readable code"
    ];
  }

  /**
   * Generate execution protocol instructions (not milestone implementation steps)
   */
  generateExecutionProtocol(
    milestone,
    agentRules,
    repoProcesses,
    agentType,
    milestoneFile
  ) {
    const timestamp = new Date().toISOString().split("T")[0];
    const relativeMilestonePath = milestoneFile.replace(
      process.cwd() + "/",
      ""
    );

    return `---
title: "Execution Instructions: ${milestone.title}"
milestone: "${milestone.id}"
agent: "${agentType}"
generated: "${timestamp}"
status: "Ready for Execution"
---

# ${milestone.title} - Execution Instructions for ${
      agentType.charAt(0).toUpperCase() + agentType.slice(1)
    }

## 🎯 Your Task

**Execute milestone**: \`${relativeMilestonePath}\`

Read the milestone specification thoroughly and implement all requirements as specified.

## 📁 Repository Structure Overview

Understanding the repository layout:
- \`docs/tech-specs/milestones/\` - Active milestone specifications
- \`docs/tech-specs/archived/\` - Completed/archived milestones
- \`work-log/\` - Execution artifacts and logs
- \`code/\` - **Implementation code and packages** (run package commands here)
- \`docs/scripts/\` - Validation and utility scripts

**Important**: Most development work happens in the \`code/\` directory

## 🛑 STOP - Complete Pre-Execution Setup First

**DO NOT START IMPLEMENTATION** until you complete ALL items below.

**MANDATORY SETUP STEPS** (complete in order):

- [ ] **Find the milestone specification**:
  - Check: \`${relativeMilestonePath}\`
  - If not found, list available milestones in \`docs/tech-specs/milestones/\`
  - Ask for clarification on which milestone to execute
- [ ] **Read agent-specific rules**: \`docs/tech-specs/process/agent-rules/${agentType}.mdx\`
- [ ] **Read core process rules**: \`docs/tech-specs/process/agent-rules/core.mdx\`
- [ ] **Read repository structure**: \`docs/tech-specs/structure.mdx\`
- [ ] **Read dependency guidelines**: \`docs/tech-specs/dependencies.mdx\`
- [ ] **Create work-log structure** following milestone-M0 audit format:
  - \`work-log/${milestone.id}/implementation-log.md\`
  - \`work-log/${milestone.id}/technical-reference.md\`
  - \`work-log/${milestone.id}/conversation-summary.md\`
  - \`work-log/${milestone.id}/fixes-checklist.md\`
- [ ] **Set up git workflow** (see Git Setup section below)
- [ ] **Verify development environment** is properly set up
- [ ] **Confirm access** to all required tools and dependencies

**⚠️ ONLY PROCEED TO EXECUTION AFTER COMPLETING ALL SETUP STEPS ABOVE**

## 🚨 If Referenced Files Don't Exist

1. **List what files ARE available** in the milestones directory
2. **Ask for clarification** with specific options found
3. **NEVER reference archived milestones** - only use active milestones in \`docs/tech-specs/milestones/\`
4. **NEVER use docs/tech-specs/archived/** - archived content is off-limits
5. **Don't analyze the entire codebase** - just report what you found

## 🔧 Git Setup (Complete Before Any Code Changes)

**MANDATORY**: Complete git setup before making any code changes:

### Git Workflow Setup
- [ ] **Create milestone branch**: Follow git workflow from \`docs/tech-specs/process/core/git-workflow.mdx\`
- [ ] **Check milestone for git strategy** - some milestones specify additional workflow requirements
- [ ] **Follow task-level git requirements** - technical specifications may define specific git workflows
- [ ] **Document git strategy** in your implementation-log.md

### Task-Based Development Workflow
- [ ] **Break milestone into tasks**: Identify discrete tasks from milestone specification
- [ ] **Create task branches**: For each task, create branch from milestone branch using format \`${
      milestone.id
    }/task-{##}-{short-description}\`
- [ ] **Work on one task at a time**: Complete each task fully before moving to next
- [ ] **Update work logs before merging**: Before merging any task branch, update ALL 4 work-log documents:
  - \`work-log/${
    milestone.id
  }/implementation-log.md\` - Document task completion and decisions
  - \`work-log/${
    milestone.id
  }/technical-reference.md\` - Add technical details and references
  - \`work-log/${
    milestone.id
  }/conversation-summary.md\` - Summarize key discussions and decisions
  - \`work-log/${
    milestone.id
  }/fixes-checklist.md\` - Document any issues found and resolved
- [ ] **Merge task to milestone**: Use squash merge to keep clean history: \`git merge --squash task-branch\`
- [ ] **Repeat for each task**: Continue until all milestone tasks are complete

**Git workflow priority**: Milestone specification > Task-level requirements > docs/tech-specs/structure.mdx > general guidelines

## 🔧 Repository Process Guidelines

Follow these repository-specific processes during execution:

**For detailed process documentation**: See \`docs/tech-specs/process/\` directory

### Package Management
- **Follow dependency guidelines**: See \`docs/tech-specs/dependencies.mdx\`
- **Use ${repoProcesses.packageManager}** for all dependency management
- **Work in the correct directory**: Most package commands should be run in \`code/\` directory
- Never edit package files manually - use package manager commands
- **Install dependencies**: \`cd code && ${
      repoProcesses.packageManager
    } install\`
- **Build packages**: \`cd code && ${repoProcesses.packageManager} build\`
- **Run tests**: \`cd code && ${repoProcesses.packageManager} test\`

### Git Workflow
- **Follow milestone-specific git strategy** if defined in the milestone specification
- **For general guidelines**: See \`docs/tech-specs/structure.mdx\` and \`docs/tech-specs/process/\`
- Use descriptive commit messages and test before committing

### Testing Procedures
${repoProcesses.testingProcedures.map((rule) => `- ${rule}`).join("\n")}

### Documentation Requirements
${repoProcesses.documentationRequirements.map((rule) => `- ${rule}`).join("\n")}

### Validation
${repoProcesses.validationScripts.map((rule) => `- ${rule}`).join("\n")}

### Code Style
${repoProcesses.codeStyle.map((rule) => `- ${rule}`).join("\n")}

## 🤖 Agent-Specific Guidelines

### Key Rules for ${
      agentType.charAt(0).toUpperCase() + agentType.slice(1)
    } Agents:

${this.formatAgentRules(agentRules)}

## ⚠️ EXECUTION ORDER - FOLLOW EXACTLY

**CRITICAL**: Follow this exact sequence. Skipping setup steps will lead to execution errors.

### Phase 1: Setup (MANDATORY - Complete First)
1. **Complete pre-execution checklist** (all items above)
2. **Set up git workflow** (create branch, confirm strategy)
3. **Create execution log** and document setup decisions

### Phase 2: Planning (MANDATORY - Complete Second)
4. **Read milestone specification** thoroughly
   - Understand all requirements and success criteria
   - Note any specific implementation guidance
   - Identify all deliverables
5. **Plan implementation approach** and document in execution log

### Phase 3: Implementation (Only After Setup + Planning)
6. **Execute using your natural workflow**
   - Use your agent's native capabilities
   - Work systematically through milestone requirements
   - Don't skip steps or make assumptions

7. **Document progress continuously**
   - Update \`work-log/${milestone.id}/implementation-log.md\` as you work
   - Maintain all 4 audit files throughout execution
   - Note any issues, decisions, or deviations
   - Track time spent on different tasks

8. **Validate completion thoroughly**
   - Check all success criteria from the milestone specification
   - Run all required tests and validation scripts
   - Ensure all deliverables are present and functional

## ⚠️ When to Stop vs Continue

- **Continue if**: Minor path differences, missing logs, unclear naming conventions
- **Stop if**: No milestone specification found anywhere, major structural confusion
- **Always**: Document assumptions and continue working when possible

## ✅ Success Criteria

Complete **all success criteria** listed in the milestone specification: \`${relativeMilestonePath}\`

The milestone specification is the authoritative source for what constitutes successful completion.

## 📝 When Complete

### Final Validation
1. **Ensure all milestone success criteria are met**
2. **Complete all 4 work-log audit files**
3. **Run acceptance tests**: If available in \`docs/scripts/acceptance/\`

### Post-Execution Updates (MANDATORY)
4. **Clean up git workflow**:
   - Follow git workflow completion steps from \`docs/tech-specs/process/core/git-workflow.mdx\`
   - Merge milestone branch to main following repository guidelines
   - Clean up branches according to established workflow
5. **Update repository documentation**:
   - Update \`docs/tech-specs/structure.mdx\` with any structural changes
   - Update \`docs/tech-specs/dependencies.mdx\` with any dependency changes
6. **Update milestone specification**:
   - Update \`${relativeMilestonePath}\` with improvements discovered during execution
   - Document any clarifications, corrections, or enhancements
   - Add lessons learned for future milestone executions
7. **Report completion**: Provide work-log summary and documentation updates

## 🚨 If Something Goes Wrong

1. **Check the milestone specification** - ensure you understand the requirements
2. **Review repository processes** - you may have missed a workflow requirement
3. **Consult agent-specific rules** - check for guidance on handling issues
4. **Document the problem** in your execution log for future improvement
5. **Ask for clarification** if requirements are unclear or conflicting

---

**Generated by**: Milestone Instruction Generator
**Source**: ${relativeMilestonePath}
**Last Updated**: ${timestamp}
`;
  }

  /**
   * Format agent rules for inclusion in instructions
   */
  formatAgentRules(agentRules) {
    if (agentRules.summary && agentRules.summary.length > 0) {
      // Always include critical file operation rules
      const criticalRules = agentRules.summary.filter(
        (rule) =>
          rule.toLowerCase().includes("file") ||
          rule.toLowerCase().includes("path") ||
          rule.toLowerCase().includes("create") ||
          rule.toLowerCase().includes("never") ||
          rule.toLowerCase().includes("always")
      );

      // Get other important rules
      const otherRules = agentRules.summary
        .filter((rule) => !criticalRules.includes(rule))
        .slice(0, 3);

      // Combine critical rules first, then other rules
      const selectedRules = [...criticalRules, ...otherRules].slice(0, 7);

      return selectedRules.map((rule) => `- ${rule}`).join("\n");
    }

    return `- Follow all guidelines in your agent-specific documentation
- Work systematically through each step
- Validate your work before proceeding
- Document any issues or questions
- ALWAYS use full file paths, never partial paths
- NEVER create files outside code/ directory without permission`;
  }

  /**
   * Write instructions to output file
   */
  async writeInstructions(instructions, outputFile) {
    const outputDir = path.dirname(outputFile);

    // Create output directory if it doesn't exist
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    fs.writeFileSync(outputFile, instructions, "utf8");
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);

  if (args.length !== 3) {
    console.error(
      "Usage: node instruction-generator.mjs <milestone-file> <agent-type> <output-file>"
    );
    process.exit(1);
  }

  const [milestoneFile, agentType, outputFile] = args;

  const generator = new InstructionGenerator();
  const success = await generator.generateInstructions(
    milestoneFile,
    agentType,
    outputFile
  );

  process.exit(success ? 0 : 1);
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error("Fatal error:", error);
    process.exit(1);
  });
}
