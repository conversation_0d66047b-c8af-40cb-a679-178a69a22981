---
title: "Execution Instructions: Milestone M1.1 — Bidirectional Sync & Incremental Diff"
milestone: "milestone-M1.2"
agent: "augment"
generated: "2025-06-01"
status: "Ready for Execution"
---

# Milestone M1.1 — Bidirectional Sync & Incremental Diff - Execution Instructions for Augment

## 🎯 Your Task

**Execute milestone**: `../docs/tech-specs/milestones/milestone-M1.2.mdx`

Read the milestone specification thoroughly and implement all requirements as specified.

## 📁 Repository Structure Overview

Understanding the repository layout:
- `docs/tech-specs/milestones/` - Active milestone specifications
- `docs/tech-specs/archived/` - Completed/archived milestones
- `work-log/` - Execution artifacts and logs
- `code/` - **Implementation code and packages** (run package commands here)
- `docs/scripts/` - Validation and utility scripts

**Important**: Most development work happens in the `code/` directory

## 🛑 STOP - Complete Pre-Execution Setup First

**DO NOT START IMPLEMENTATION** until you complete ALL items below.

**MANDATORY SETUP STEPS** (complete in order):

- [ ] **Find the milestone specification**:
  - Check: `../docs/tech-specs/milestones/milestone-M1.2.mdx`
  - If not found, list available milestones in `docs/tech-specs/milestones/`
  - Ask for clarification on which milestone to execute
- [ ] **Read agent-specific rules**: `docs/tech-specs/process/agent-rules/augment.mdx`
- [ ] **Read core process rules**: `docs/tech-specs/process/agent-rules/core.mdx`
- [ ] **Read repository structure**: `docs/tech-specs/structure.mdx`
- [ ] **Read dependency guidelines**: `docs/tech-specs/dependencies.mdx`
- [ ] **Create work-log structure** following milestone-M0 audit format:
  - `work-log/milestone-M1.2/implementation-log.md`
  - `work-log/milestone-M1.2/technical-reference.md`
  - `work-log/milestone-M1.2/conversation-summary.md`
  - `work-log/milestone-M1.2/fixes-checklist.md`
- [ ] **Set up git workflow** (see Git Setup section below)
- [ ] **Verify development environment** is properly set up
- [ ] **Confirm access** to all required tools and dependencies

**⚠️ ONLY PROCEED TO EXECUTION AFTER COMPLETING ALL SETUP STEPS ABOVE**

## 🚨 If Referenced Files Don't Exist

1. **List what files ARE available** in the milestones directory
2. **Ask for clarification** with specific options found
3. **NEVER reference archived milestones** - only use active milestones in `docs/tech-specs/milestones/`
4. **NEVER use docs/tech-specs/archived/** - archived content is off-limits
5. **Don't analyze the entire codebase** - just report what you found

## 🔧 Git Setup (Complete Before Any Code Changes)

**MANDATORY**: Complete git setup before making any code changes:

### Git Workflow Setup
- [ ] **Create milestone branch**: Follow git workflow from `docs/tech-specs/process/core/git-workflow.mdx`
- [ ] **Check milestone for git strategy** - some milestones specify additional workflow requirements
- [ ] **Follow task-level git requirements** - technical specifications may define specific git workflows
- [ ] **Document git strategy** in your implementation-log.md

### Task-Based Development Workflow
- [ ] **Break milestone into tasks**: Identify discrete tasks from milestone specification
- [ ] **Create task branches**: For each task, create branch from milestone branch using format `milestone-M1.2/task-{##}-{short-description}`
- [ ] **Work on one task at a time**: Complete each task fully before moving to next
- [ ] **Update work logs before merging**: Before merging any task branch, update ALL 4 work-log documents:
  - `work-log/milestone-M1.2/implementation-log.md` - Document task completion and decisions
  - `work-log/milestone-M1.2/technical-reference.md` - Add technical details and references
  - `work-log/milestone-M1.2/conversation-summary.md` - Summarize key discussions and decisions
  - `work-log/milestone-M1.2/fixes-checklist.md` - Document any issues found and resolved
- [ ] **Merge task to milestone**: Use squash merge to keep clean history: `git merge --squash task-branch`
- [ ] **Repeat for each task**: Continue until all milestone tasks are complete

**Git workflow priority**: Milestone specification > Task-level requirements > docs/tech-specs/structure.mdx > general guidelines

## 🔧 Repository Process Guidelines

Follow these repository-specific processes during execution:

**For detailed process documentation**: See `docs/tech-specs/process/` directory

### Package Management
- **Follow dependency guidelines**: See `docs/tech-specs/dependencies.mdx`
- **Use pnpm** for all dependency management
- **Work in the correct directory**: Most package commands should be run in `code/` directory
- Never edit package files manually - use package manager commands
- **Install dependencies**: `cd code && pnpm install`
- **Build packages**: `cd code && pnpm build`
- **Run tests**: `cd code && pnpm test`

### Git Workflow
- **Follow milestone-specific git strategy** if defined in the milestone specification
- **For general guidelines**: See `docs/tech-specs/structure.mdx` and `docs/tech-specs/process/`
- Use descriptive commit messages and test before committing

### Testing Procedures
- Run acceptance tests after implementation

### Documentation Requirements
- Update work-logs in real-time during implementation
- Document any issues or deviations
- Create execution log for milestone

### Validation
- Use validation scripts in docs/scripts/ if available
- Check milestone success criteria
- Verify all deliverables are present

### Code Style
- Follow existing code patterns in the repository
- Use consistent naming conventions
- Maintain clean, readable code

## 🤖 Agent-Specific Guidelines

### Key Rules for Augment Agents:

- **Create requirement checklist** using templates
- **Use view** to examine files before modification
- **Use codebase-retrieval** to understand requirements and context
- **Use view** to examine milestone specification thoroughly
- **Plan implementation approach** based on existing patterns

## ⚠️ EXECUTION ORDER - FOLLOW EXACTLY

**CRITICAL**: Follow this exact sequence. Skipping setup steps will lead to execution errors.

### Phase 1: Setup (MANDATORY - Complete First)
1. **Complete pre-execution checklist** (all items above)
2. **Set up git workflow** (create branch, confirm strategy)
3. **Create execution log** and document setup decisions

### Phase 2: Planning (MANDATORY - Complete Second)
4. **Read milestone specification** thoroughly
   - Understand all requirements and success criteria
   - Note any specific implementation guidance
   - Identify all deliverables
5. **Plan implementation approach** and document in execution log

### Phase 3: Implementation (Only After Setup + Planning)
6. **Break milestone into discrete tasks**
   - Identify individual tasks from milestone specification
   - Plan task sequence and dependencies
   - Estimate effort for each task

7. **Execute using task-based workflow**
   - Create task branch: `git checkout -b milestone-M1.2/task-01-{description}`
   - Work on ONE task at a time until completion
   - Use your agent's native capabilities systematically
   - Don't skip steps or make assumptions

8. **Complete task cycle for each task**
   - Implement the task fully
   - Test the task implementation
   - Update ALL 4 work-log documents before merging:
     - `work-log/milestone-M1.2/implementation-log.md` - Task completion details
     - `work-log/milestone-M1.2/technical-reference.md` - Technical details added
     - `work-log/milestone-M1.2/conversation-summary.md` - Key decisions made
     - `work-log/milestone-M1.2/fixes-checklist.md` - Issues found and resolved
   - Merge task to milestone: `git checkout milestone-branch && git merge --squash task-branch`
   - Commit with descriptive message: `git commit -m "feat(scope): task description"`

9. **Validate milestone completion thoroughly**
   - Check all success criteria from the milestone specification
   - Run all required tests and validation scripts
   - Ensure all deliverables are present and functional
   - Verify all work-log documents are complete and up-to-date

## ⚠️ When to Stop vs Continue

- **Continue if**: Minor path differences, missing logs, unclear naming conventions
- **Stop if**: No milestone specification found anywhere, major structural confusion
- **Always**: Document assumptions and continue working when possible

## ✅ Success Criteria

Complete **all success criteria** listed in the milestone specification: `../docs/tech-specs/milestones/milestone-M1.2.mdx`

The milestone specification is the authoritative source for what constitutes successful completion.

## 📝 When Complete

### Final Validation
1. **Ensure all milestone success criteria are met**
2. **Complete all 4 work-log audit files**
3. **Run acceptance tests**: If available in `docs/scripts/acceptance/`

### Post-Execution Updates (MANDATORY)
4. **Clean up git workflow**:
   - Follow git workflow completion steps from `docs/tech-specs/process/core/git-workflow.mdx`
   - Merge milestone branch to main following repository guidelines
   - Clean up branches according to established workflow
5. **Update repository documentation**:
   - Update `docs/tech-specs/structure.mdx` with any structural changes
   - Update `docs/tech-specs/dependencies.mdx` with any dependency changes
6. **Update milestone specification**:
   - Update `../docs/tech-specs/milestones/milestone-M1.2.mdx` with improvements discovered during execution
   - Document any clarifications, corrections, or enhancements
   - Add lessons learned for future milestone executions
7. **Report completion**: Provide work-log summary and documentation updates

## 🚨 If Something Goes Wrong

1. **Check the milestone specification** - ensure you understand the requirements
2. **Review repository processes** - you may have missed a workflow requirement
3. **Consult agent-specific rules** - check for guidance on handling issues
4. **Document the problem** in your execution log for future improvement
5. **Ask for clarification** if requirements are unclear or conflicting

---

**Generated by**: Milestone Instruction Generator
**Source**: ../docs/tech-specs/milestones/milestone-M1.2.mdx
**Last Updated**: 2025-06-01
