# Technical Reference - Milestone M1.2

## Architecture Overview

### Core Components
- **kg-sync-lib**: New package for bidirectional sync
- **kg-cli**: Enhanced CLI with sync-kg command
- **GitHub Actions**: CI/CD workflow for validation

### Key Technologies
- **simple-git**: Git diff integration (v3.22.0)
- **comment-parser**: JSDoc annotation parsing (v1.4.0)
- **TypeScript**: Implementation language (v5.4.3)
- **Jest**: Testing framework (v29.7.0)

## Technical Specifications

### Annotation Format
```typescript
/**
 * @implements milestone-M1.2#ComponentName
 */
```

### Git Diff Strategy
- Incremental processing using `git diff --name-only`
- Support for `--since HEAD~1`, `--since origin/main`
- Edge case handling for merge conflicts, renames, binary files

### Confidence Scoring
- **1.0**: Valid annotation, function exists, recently verified
- **0.8**: Valid annotation, function exists, not recently verified (>30 days)
- **0.5**: Annotation exists but has validation warnings
- **0.2**: Annotation removed but function still exists (stale)
- **0.1**: Function deleted but edge preserved for history (stale)

### Coverage Calculation
```typescript
coverage = implementedComponents / totalComponents
```

## API Reference

### parseAnnotations(content: string): Annotation[]
Parses JSDoc comments to extract @implements annotations.

### diffGit(since: string): string[]
Gets list of changed files since specified git reference.

### updateGraph(annotations: Annotation[], changedFiles: string[]): GraphUpdateResult
Updates knowledge graph with new annotations and confidence scores.

### calculateCoverage(milestoneId: string): MilestoneCoverage
Calculates implementation coverage for a milestone.

## File Structure
```
code/packages/kg-sync-lib/
├── src/
│   ├── diffGit.ts
│   ├── parseAnnotations.ts
│   ├── updateGraph.ts
│   └── index.ts
├── tests/
└── package.json
```

## Related Documentation
- ADR-006: Bidirectional Sync Architecture
- ADR-007: JSDoc Annotation Parsing Strategy
- ADR-008: Git Diff Integration for Incremental Updates
- Milestone M1.2 Specification
