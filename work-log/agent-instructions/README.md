# Updated Agent Instructions

## Summary of Changes Made

### 1. Removed Worktree Creation Rules
- **Removed**: All references to `git worktree add` and workspace isolation
- **Replaced with**: Standard git workflow using milestone and task branches
- **Benefit**: Eliminates path confusion and simplifies monitoring

### 2. Added Task-Based Development Workflow
- **Break milestones into discrete tasks**: Each milestone should be divided into manageable, individual tasks
- **Create task branches**: Use format `{milestone_id}/task-{##}-{short-description}` from milestone branch
- **Work on one task at a time**: Complete each task fully before moving to the next
- **Update work logs before merging**: Before merging any task branch, update ALL 4 work-log documents:
  - `implementation-log.md` - Document task completion and decisions
  - `technical-reference.md` - Add technical details and references
  - `conversation-summary.md` - Summarize key discussions and decisions
  - `fixes-checklist.md` - Document any issues found and resolved
- **Merge with squash**: Use `git merge --squash task-branch` to maintain clean history
- **Conventional commits**: Use format `type(scope): description`

### 3. Enhanced File Operation Rules
- **ALWAYS use full file paths**: Never use partial or relative paths
- **NEVER create files at root level**: Without explicit permission
- **NEVER create files outside code/ directory**: Without explicit permission
- **ASK for permission**: Before creating files outside code/ directory
- **VERIFY file paths**: Double-check all file paths before operations

### 4. Updated Agent Rules
Enhanced the following agent rule files:
- `docs/tech-specs/process/agent-rules/core.mdx` - Added file operation and task workflow rules
- `docs/tech-specs/process/agent-rules/augment.mdx` - Added agent-specific implementations

### 5. Generated Updated Instructions
Created updated instruction files for all three agents:
- `instructions-for-augment.md` - Augment Agent instructions
- `instructions-for-cursor.md` - Cursor Agent instructions  
- `instructions-for-copilot.md` - GitHub Copilot instructions

## Key Workflow Changes

### Before (Old Workflow)
1. Create worktree workspace
2. Work in isolated directory
3. Merge back to main repo
4. Clean up worktree

### After (New Workflow)
1. Create milestone branch from main
2. Break milestone into tasks
3. For each task:
   - Create task branch from milestone branch
   - Implement task
   - Update all 4 work-log documents
   - Merge task to milestone with squash
4. When milestone complete, merge to main

## Benefits of Changes

1. **Simplified Path Management**: No more confusion about working directories
2. **Better Task Tracking**: Clear task-by-task progress with documentation
3. **Improved Documentation**: Mandatory work-log updates ensure comprehensive tracking
4. **File Safety**: Strict rules prevent accidental file creation in wrong locations
5. **Clean Git History**: Squash merges maintain readable commit history
6. **Consistent Workflow**: Same process across all agents (Augment, Cursor, Copilot)

## Usage

To generate instructions for any agent:
```bash
cd docs/scripts
node instruction-generator.mjs ../tech-specs/milestones/{milestone-file}.mdx {agent-type} {output-file}
```

Example:
```bash
node instruction-generator.mjs ../tech-specs/milestones/milestone-M1.1.mdx augment ../../work-log/m1.1/instructions-for-augment.md
```

## Files Modified

### Core Files
- `docs/scripts/instruction-generator.mjs` - Updated instruction generation logic
- `docs/tech-specs/process/agent-rules/core.mdx` - Added universal rules
- `docs/tech-specs/process/agent-rules/augment.mdx` - Enhanced Augment-specific rules

### Generated Instructions
- `work-log/agent-instructions/instructions-for-augment.md`
- `work-log/agent-instructions/instructions-for-cursor.md`
- `work-log/agent-instructions/instructions-for-copilot.md`

---

**Last Updated**: 2025-06-01
**Generated by**: Augment Agent
**Status**: Ready for use
