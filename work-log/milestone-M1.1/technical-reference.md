# Technical Reference: Milestone M1.1 - Static Code Parser & Graph Augmenter

## 🎯 Technical Overview

### Scope
Parse Python + JavaScript source files with Tree-sitter, extract functions & call-graph, and merge results into the existing KG (kg.jsonld / kg.yaml).

### Key Technologies
- **Tree-sitter**: Multi-language AST parsing
- **Node.js**: Runtime environment
- **TypeScript**: Type-safe development
- **Jest**: Testing framework
- **pnpm**: Package management

## 🧳 Toolchain Specifications

```yaml
node: "20.11.0"
pnpm: "8.15.4"
typescript: "5.4.3"
tree-sitter-cli: "0.25.4"
tree-sitter: "0.22.3"           # Node bindings
tree-sitter-python: "0.23.6"
tree-sitter-javascript: "0.23.1"
jest: "29.7.0"
esbuild-register: "3.4.2"        # for ts-node-like import in CLI tests
```

## 🏗️ Architecture Design

### Package Structure
```text
code/packages/
├─ code-parser-lib/           # NEW
│   ├─ src/
│   │   ├─ parseFile.ts       # Function extraction
│   │   ├─ callGraph.ts       # Call relationship extraction
│   │   └─ index.ts           # Public API
│   ├─ tests/                 # Unit tests
│   └─ package.json
└─ kg-cli/                    # ENHANCED
    ├─ src/
    │   ├─ build-kg.ts        # Enhanced with --code flag
    │   └─ index.ts
    └─ package.json
```

### Data Flow
1. **Input**: Source files (.py, .js, .mjs, .cjs)
2. **Parse**: Tree-sitter AST generation
3. **Extract**: Function nodes and call edges
4. **Merge**: Integration with existing knowledge graph
5. **Output**: Updated kg.jsonld and kg.yaml

## 🧠 Key Architectural Decisions

| Topic                | Decision                                              | Rationale                                         |
|----------------------|------------------------------------------------------|---------------------------------------------------|
| Languages in scope   | Python (.py) + JavaScript (.js, .mjs, .cjs)          | Most common in repo, quick Tree-sitter support.    |
| Edge granularity     | Only direct, same-file calls for M1; no dynamic dispatch. | Simpler first slice; indirect edges will come in M1.1. |
| Graph merge strategy | Read existing kg.jsonld → append new nodes/edges → overwrite. | Idempotent; dry-run uses in-memory merge only.     |
| Deterministic IDs    | function:<hash(filepath+name+startLine)>             | Prevent dupes on re-parse; stable across branches. |

## 📊 Data Models

### Function Node Schema
```typescript
interface FunctionNode {
  "@id": string;                    // function:<hash(filepath+name+startLine)>
  "@type": "function";
  name: string;                     // Function name
  signature: string;                // Function signature
  file: string;                     // Relative file path
  lang: "python" | "javascript";   // Source language
  line_start: number;               // Starting line number
  line_end: number;                 // Ending line number
}
```

### Workflow Call Edge Schema
```typescript
interface WorkflowCallEdge {
  "@id": string;                    // Unique edge identifier
  "@type": "workflow_calls";
  source: string;                   // Source function @id
  target: string;                   // Target function @id
  call_type: "direct";              // Call type (direct only for M1)
  confidence: 1.0;                  // Confidence score
  file: string;                     // File where call occurs
  line: number;                     // Line number of call
}
```

## 🤖 CLI Interface

### New Flags
| Flag               | Type           | Description                                         |
|--------------------|----------------|-----------------------------------------------------|
| --code <dir>       | path           | Directory to scan for .py & .js files.              |
| --languages py,js  | list (optional)| Comma-separated whitelist; default py,js.           |
| --dry-run          | boolean        | Parse & merge in-memory; print summary.             |

### Usage Examples
```bash
# Scan code directory and update knowledge graph
pnpm run build-kg -- --code src/

# Dry-run with stats output
pnpm run build-kg -- --code tests/fixtures --dry-run

# Scan specific languages only
pnpm run build-kg -- --code src/ --languages py
```

### Dry-run Output Format
```yaml
Functions added:  42
workflow_calls added:  87
Files scanned:    15
Skipped (parse error): 1
Exit status: 0 success, 1 parse error fatal.
```

## 🧪 Testing Strategy

### Test Fixtures
- `tests/fixtures/python/hello.py` - Simple Python file with ≥1 function
- `tests/fixtures/javascript/hello.js` - Simple JavaScript file with ≥1 function

### Test Coverage Targets
- Unit tests: ≥80% coverage for code-parser-lib
- Integration tests: CLI integration with kg-cli
- Acceptance tests: End-to-end validation

### Test Categories
1. **Unit Tests**: parseFile.ts and callGraph.ts functionality
2. **Integration Tests**: CLI flag integration and graph merging
3. **Acceptance Tests**: Complete workflow validation

## 🔄 CI/CD Pipeline

### Code Parse Workflow
```yaml
name: Code Parse
on: [push, pull_request]

jobs:
  code-parse:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v2
        with: { version: 8.15.4 }
      - run: corepack enable && pnpm install
      - run: pnpm run build-kg -- --code tests/fixtures --dry-run
```

## 🔍 Implementation Details

### Tree-sitter Integration
- Use official Tree-sitter Node.js bindings
- Compile Python and JavaScript grammars
- Parse AST for function declarations and call expressions

### Function Extraction Algorithm
1. Parse source file with Tree-sitter
2. Traverse AST for function declarations
3. Extract function metadata (name, signature, location)
4. Generate deterministic function ID

### Call Graph Extraction Algorithm
1. Parse source file with Tree-sitter
2. Traverse AST for call expressions
3. Match calls to known functions (same-file only)
4. Generate call edges with metadata

### Graph Merge Strategy
1. Read existing kg.jsonld
2. Parse and extract new nodes/edges
3. Merge without duplicates (using deterministic IDs)
4. Write updated kg.jsonld and kg.yaml

## 📚 Dependencies

### New Dependencies for code-parser-lib
- `tree-sitter`: "0.22.3" - Core Tree-sitter bindings
- `tree-sitter-python`: "0.23.6" - Python grammar
- `tree-sitter-javascript`: "0.23.1" - JavaScript grammar

### Build Dependencies
- `tree-sitter-cli`: "0.25.4" - Grammar compilation
- `esbuild-register`: "3.4.2" - TypeScript execution for tests

## 🚨 Risk Considerations

### Technical Risks
- Tree-sitter grammar compatibility
- AST parsing accuracy for complex code
- Performance with large codebases
- Graph merge correctness

### Mitigation Strategies
- Use exact versions specified in toolchain
- Comprehensive test coverage with edge cases
- Incremental testing with small examples
- Validation against known good outputs

## 📖 References

### External Documentation
- [Tree-sitter Documentation](https://tree-sitter.github.io/tree-sitter/)
- [Tree-sitter Node.js Bindings](https://github.com/tree-sitter/node-tree-sitter)
- [Tree-sitter Python Grammar](https://github.com/tree-sitter/tree-sitter-python)
- [Tree-sitter JavaScript Grammar](https://github.com/tree-sitter/tree-sitter-javascript)

### Internal References
- Milestone specification: `docs/tech-specs/milestones/milestone-M1.1.mdx`
- Repository structure: `docs/tech-specs/structure.mdx`
- Dependency guidelines: `docs/tech-specs/dependencies.mdx`
