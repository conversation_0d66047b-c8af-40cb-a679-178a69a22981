---
title: "Execution Instructions: Milestone M1 — Static Code Parser & Graph Augmenter"
milestone: "milestone-M1.1"
agent: "augment"
generated: "2025-06-01"
status: "Ready for Execution"
---

# Milestone M1 — Static Code Parser & Graph Augmenter - Execution Instructions for Augment

## 🎯 Your Task

**Execute milestone**: `docs/tech-specs/milestones/milestone-M1.1.mdx`

Read the milestone specification thoroughly and implement all requirements as specified.

## 📁 Repository Structure Overview

Understanding the repository layout:
- `docs/tech-specs/milestones/` - Active milestone specifications
- `docs/tech-specs/archived/` - Completed/archived milestones
- `work-log/` - Execution artifacts and logs
- `code/` - **Implementation code and packages** (run package commands here)
- `docs/scripts/` - Validation and utility scripts

**Important**: Most development work happens in the `code/` directory

## 🛑 STOP - Complete Pre-Execution Setup First

**DO NOT START IMPLEMENTATION** until you complete ALL items below.

**MANDATORY SETUP STEPS** (complete in order):

- [ ] **Find the milestone specification**:
  - Check: `docs/tech-specs/milestones/milestone-M1.1.mdx`
  - If not found, list available milestones in `docs/tech-specs/milestones/`
  - Ask for clarification on which milestone to execute
- [ ] **Read agent-specific rules**: `docs/tech-specs/process/agent-rules/augment.mdx`
- [ ] **Read core process rules**: `docs/tech-specs/process/agent-rules/core.mdx`
- [ ] **Read repository structure**: `docs/tech-specs/structure.mdx`
- [ ] **Read dependency guidelines**: `docs/tech-specs/dependencies.mdx`
- [ ] **Create work-log structure** following milestone-M0 audit format:
  - `work-log/milestone-M1.1/implementation-log.md`
  - `work-log/milestone-M1.1/technical-reference.md`
  - `work-log/milestone-M1.1/conversation-summary.md`
  - `work-log/milestone-M1.1/fixes-checklist.md`
- [ ] **Set up git workflow** (see Git Setup section below)
- [ ] **Verify development environment** is properly set up
- [ ] **Confirm access** to all required tools and dependencies

**⚠️ ONLY PROCEED TO EXECUTION AFTER COMPLETING ALL SETUP STEPS ABOVE**

## 🚨 If Referenced Files Don't Exist

1. **List what files ARE available** in the milestones directory
2. **Ask for clarification** with specific options found
3. **NEVER reference archived milestones** - only use active milestones in `docs/tech-specs/milestones/`
4. **NEVER use docs/tech-specs/archived/** - archived content is off-limits
5. **Don't analyze the entire codebase** - just report what you found

## 🔧 Git Setup (Complete Before Any Code Changes)

**MANDATORY**: Complete git setup before making any code changes:

### Isolated Workspace Setup
- [ ] **Create isolated workspace**: `git worktree add ../milestone-M1.1-workspace milestone-M1.1`
- [ ] **Switch to workspace**: `cd ../milestone-M1.1-workspace`
- [ ] **Verify isolation**: Confirm you're in separate directory (`pwd` should show workspace path)
- [ ] **Check milestone for git strategy** - some milestones specify additional workflow requirements
- [ ] **Follow task-level git requirements** - technical specifications may define specific git workflows
- [ ] **Document workspace path** in your implementation-log.md

### When Milestone Complete
- [ ] **Return to main repo**: `cd ../kloudi-swe-agent`
- [ ] **Merge changes**: `git merge milestone-M1.1`
- [ ] **Remove workspace**: `git worktree remove ../milestone-M1.1-workspace`
- [ ] **Delete branch**: `git branch -d milestone-M1.1`

**Git workflow priority**: Milestone specification > Task-level requirements > docs/tech-specs/structure.mdx > general guidelines

## 🔧 Repository Process Guidelines

Follow these repository-specific processes during execution:

**For detailed process documentation**: See `docs/tech-specs/process/` directory

### Package Management
- **Follow dependency guidelines**: See `docs/tech-specs/dependencies.mdx`
- **Use pnpm** for all dependency management
- **Work in the correct directory**: Most package commands should be run in `code/` directory
- Never edit package files manually - use package manager commands
- **Install dependencies**: `cd code && pnpm install`
- **Build packages**: `cd code && pnpm build`
- **Run tests**: `cd code && pnpm test`

### Git Workflow
- **Follow milestone-specific git strategy** if defined in the milestone specification
- **For general guidelines**: See `docs/tech-specs/structure.mdx` and `docs/tech-specs/process/`
- Use descriptive commit messages and test before committing

### Testing Procedures
- Run acceptance tests after implementation

### Documentation Requirements
- Update work-logs in real-time during implementation
- Document any issues or deviations
- Create execution log for milestone

### Validation
- Use validation scripts in docs/scripts/ if available
- Check milestone success criteria
- Verify all deliverables are present

### Code Style
- Follow existing code patterns in the repository
- Use consistent naming conventions
- Maintain clean, readable code

## 🤖 Agent-Specific Guidelines

### Key Rules for Augment Agents:

- **Use codebase-retrieval** to understand requirements and context
- **Use view** to examine milestone specification thoroughly
- **Create requirement checklist** using templates
- **Plan implementation approach** based on existing patterns
- **Use codebase-retrieval** to find relevant code patterns

## ⚠️ EXECUTION ORDER - FOLLOW EXACTLY

**CRITICAL**: Follow this exact sequence. Skipping setup steps will lead to execution errors.

### Phase 1: Setup (MANDATORY - Complete First)
1. **Complete pre-execution checklist** (all items above)
2. **Set up git workflow** (create branch, confirm strategy)
3. **Create execution log** and document setup decisions

### Phase 2: Planning (MANDATORY - Complete Second)
4. **Read milestone specification** thoroughly
   - Understand all requirements and success criteria
   - Note any specific implementation guidance
   - Identify all deliverables
5. **Plan implementation approach** and document in execution log

### Phase 3: Implementation (Only After Setup + Planning)
6. **Execute using your natural workflow**
   - Use your agent's native capabilities
   - Work systematically through milestone requirements
   - Don't skip steps or make assumptions

7. **Document progress continuously**
   - Update `work-log/milestone-M1.1/implementation-log.md` as you work
   - Maintain all 4 audit files throughout execution
   - Note any issues, decisions, or deviations
   - Track time spent on different tasks

8. **Validate completion thoroughly**
   - Check all success criteria from the milestone specification
   - Run all required tests and validation scripts
   - Ensure all deliverables are present and functional

## ⚠️ When to Stop vs Continue

- **Continue if**: Minor path differences, missing logs, unclear naming conventions
- **Stop if**: No milestone specification found anywhere, major structural confusion
- **Always**: Document assumptions and continue working when possible

## ✅ Success Criteria

Complete **all success criteria** listed in the milestone specification: `docs/tech-specs/milestones/milestone-M1.1.mdx`

The milestone specification is the authoritative source for what constitutes successful completion.

## 📝 When Complete

### Final Validation
1. **Ensure all milestone success criteria are met**
2. **Complete all 4 work-log audit files**
3. **Run acceptance tests**: If available in `docs/scripts/acceptance/`

### Post-Execution Updates (MANDATORY)
4. **Clean up workspace**:
   - Return to main repo: `cd ../kloudi-swe-agent`
   - Merge changes: `git merge milestone-M1.1`
   - Remove workspace: `git worktree remove ../milestone-M1.1-workspace`
   - Delete branch: `git branch -d milestone-M1.1`
5. **Update repository documentation**:
   - Update `docs/tech-specs/structure.mdx` with any structural changes
   - Update `docs/tech-specs/dependencies.mdx` with any dependency changes
6. **Update milestone specification**:
   - Update `docs/tech-specs/milestones/milestone-M1.1.mdx` with improvements discovered during execution
   - Document any clarifications, corrections, or enhancements
   - Add lessons learned for future milestone executions
7. **Report completion**: Provide work-log summary and documentation updates

## 🚨 If Something Goes Wrong

1. **Check the milestone specification** - ensure you understand the requirements
2. **Review repository processes** - you may have missed a workflow requirement
3. **Consult agent-specific rules** - check for guidance on handling issues
4. **Document the problem** in your execution log for future improvement
5. **Ask for clarification** if requirements are unclear or conflicting

---

**Generated by**: Milestone Instruction Generator
**Source**: docs/tech-specs/milestones/milestone-M1.1.mdx
**Last Updated**: 2025-06-01
