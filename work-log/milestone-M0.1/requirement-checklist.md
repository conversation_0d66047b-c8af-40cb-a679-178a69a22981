# Requirement Checklist: Milestone M0.1 — Knowledge-Graph Bootstrap

**Milestone**: M0.1 — Knowledge-Graph Bootstrap  
**Agent**: Augment  
**Created**: 2025-01-25  
**Status**: Planning  

---

## 📋 SUCCESS CRITERIA CHECKLIST

### SC-1: Dry-run Command
- [ ] `pnpm run build-kg -- --dry-run docs/tech-specs` exits 0
- [ ] Command prints summary without writing files
- [ ] No kg.jsonld or kg.yaml files created during dry-run

### SC-2: Full Graph Build
- [ ] Running without `--dry-run` writes both graph files
- [ ] `kg.jsonld` file created in repo root
- [ ] `kg.yaml` file created in repo root
- [ ] Both files contain valid graph data

### SC-3: CI Integration
- [ ] `.github/workflows/graph.yml` job passes on PR & push
- [ ] CI runs `pnpm run build-kg -- --dry-run docs/tech-specs`
- [ ] CI job uses correct Node.js and pnpm versions

### SC-4: Graph Content Validation
- [ ] `kg.yaml` shows at least one milestone node (M0)
- [ ] `kg.yaml` shows at least one component node
- [ ] `kg.yaml` shows at least one implements edge
- [ ] Graph structure follows kg-schema.yml

### SC-5: Spec Lint Validation
- [ ] `node scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx` passes
- [ ] Milestone specification passes all validation rules

### SC-6: Agent Dry-run Validation
- [ ] `pnpm run agent:dry-run --spec docs/tech-specs/milestones/milestone-M0.1.mdx` passes
- [ ] Agent validation script exists and works

---

## 📦 DELIVERABLES CHECKLIST

### Package: spec-parser-lib
- [ ] `code/packages/spec-parser-lib/package.json` with correct configuration
- [ ] `code/packages/spec-parser-lib/src/parse-specs.ts` main parsing logic
- [ ] `code/packages/spec-parser-lib/src/index.ts` exports
- [ ] `code/packages/spec-parser-lib/tsconfig.json` TypeScript configuration
- [ ] Jest tests for parsing functionality
- [ ] Build script using tsup
- [ ] Type checking script
- [ ] Coverage threshold configuration

### Package: kg-cli
- [ ] `code/packages/kg-cli/package.json` with correct configuration
- [ ] `code/packages/kg-cli/src/build-kg.ts` CLI implementation
- [ ] `code/packages/kg-cli/src/index.ts` exports
- [ ] `code/packages/kg-cli/tsconfig.json` TypeScript configuration
- [ ] Jest tests for CLI functionality
- [ ] Build script using tsup
- [ ] Executable CLI binary configuration

### Schema & Output Files
- [ ] `kg-schema.yml` in repo root with entities & relationships
- [ ] `kg.jsonld` output file (generated)
- [ ] `kg.yaml` output file (generated)

### CI/CD Configuration
- [ ] `.github/workflows/graph.yml` workflow file
- [ ] Workflow uses Node 20.11.0 and pnpm 8.15.4
- [ ] Workflow runs on push and pull_request
- [ ] Workflow includes dry-run validation

### Editor Support
- [ ] `.vscode/extensions.json` with MDX + Markdown preview extensions
- [ ] `docs/README.md` quick-start guide for VS Code/Obsidian

### Root Package Configuration
- [ ] `pnpm run build-kg` script added to root package.json
- [ ] Script supports `--dry-run` flag
- [ ] Script targets `docs/tech-specs` directory

---

## 🔧 TECHNICAL REQUIREMENTS

### Dependencies (from milestone spec)
- [ ] `gray-matter: "4.1.0"` for front-matter parsing
- [ ] `yaml: "2.3.2"` for YAML emit
- [ ] `uuid: "9.0.0"` for deterministic IDs
- [ ] TypeScript 5.4.3 compatibility
- [ ] Node 20.11.0 compatibility
- [ ] pnpm 8.15.4 compatibility

### Package Structure Patterns (from codebase analysis)
- [ ] Follow existing package.json structure from shared package
- [ ] Use tsup for building with ESM format and .d.ts generation
- [ ] Use Jest for spec-parser-lib tests (backend pattern)
- [ ] Use vitest for kg-cli tests if appropriate
- [ ] Include coverage thresholds (80% for unit tests)
- [ ] Use conventional naming: `@workflow-mapper/package-name`

### Build & Test Configuration
- [ ] TypeScript configuration extends root tsconfig.json
- [ ] ESLint compliance with existing rules
- [ ] Prettier formatting consistency
- [ ] Coverage reporting with lcov and html formats
- [ ] Test files follow `*.test.ts` pattern

---

## 🚀 IMPLEMENTATION PLAN

### Phase 1: Package Scaffolding
1. [ ] Create spec-parser-lib package structure
2. [ ] Create kg-cli package structure
3. [ ] Configure package.json files
4. [ ] Set up TypeScript configurations
5. [ ] Add to pnpm workspace

### Phase 2: Core Implementation
1. [ ] Implement MDX front-matter parsing
2. [ ] Implement heading extraction
3. [ ] Create kg-schema.yml
4. [ ] Implement CLI build-kg.ts
5. [ ] Add graph output functionality

### Phase 3: Testing & Validation
1. [ ] Write Jest tests for parser
2. [ ] Write tests for CLI
3. [ ] Set up coverage reporting
4. [ ] Validate against success criteria

### Phase 4: CI & Documentation
1. [ ] Create GitHub Actions workflow
2. [ ] Add VS Code extensions configuration
3. [ ] Create docs/README.md
4. [ ] Final validation and testing

---

## 📝 NOTES & DECISIONS

### Package Manager Commands
- Use `pnpm add` for dependencies (never edit package.json manually)
- Work in `code/` directory for all package commands
- Follow existing patterns from shared and api packages

### Testing Strategy
- spec-parser-lib: Jest (follows backend pattern)
- kg-cli: Jest (CLI testing with supertest if needed)
- Coverage thresholds: 80% minimum for new packages

### Build Strategy
- Use tsup for both packages (consistent with existing)
- ESM format output
- Generate .d.ts files for TypeScript support
- Clean builds with --clean flag

---

**Created**: 2025-01-25  
**Last Updated**: 2025-01-25  
**Status**: Ready for Implementation
